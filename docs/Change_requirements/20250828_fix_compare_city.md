# 需求 [fix_compare_city]

## 反馈

1. 源数据带有经纬度的房源,由于boundary city比较没有考虑拼写错误以及subCity,会造成geocoding次数增加,需要进行修复

## 需求提出人:    Fred

## 修改人：       <PERSON><PERSON>ei

## 提出日期:      2025-08-28

## 解决办法

1. 添加city比较函数(isSameCity),在比较city一致时使用该函数,添加在`src/lib/cityHelper.coffee`下。
2. 函数逻辑:
  1. city一致返回true
  2. city相似(90%)返回true
  3. 判断subcity并比较, eg: Scarborough == Toronto
  4. subcity判断需要考虑拼写错误问题
3. 在比较city与Boundary City时,调用该函数

## 是否需要补充UT

1. 需要补充UT

## 确认日期:    2025-08-28

## online-step
