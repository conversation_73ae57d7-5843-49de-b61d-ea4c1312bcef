should = require 'should'
cityHelper = require '../../lib/cityHelper'

describe 'cityHelper', ->

  describe 'isSameCity', ->

    tests = [
      # 基本功能测试
      {
        description: '城市名称完全一致时应该返回true',
        city1: 'Toronto',
        city2: 'Toronto',
        expected: true
      },
      {
        description: '城市名称大小写不同但内容一致时应该返回true',
        city1: 'toronto',
        city2: 'TORONTO',
        expected: true
      },
      {
        description: '城市名称有前后空格时应该返回true',
        city1: ' Toronto ',
        city2: 'Toronto',
        expected: true
      },
      {
        description: '完全不同的城市名称时应该返回false',
        city1: 'Toronto',
        city2: 'Vancouver',
        expected: false
      },

      # 参数验证测试
      {
        description: '第一个参数为空字符串时应该返回false',
        city1: '',
        city2: 'Toronto',
        expected: false
      },
      {
        description: '第二个参数为空字符串时应该返回false',
        city1: 'Toronto',
        city2: '',
        expected: false
      },
      {
        description: '两个参数都为空字符串时应该返回false',
        city1: '',
        city2: '',
        expected: false
      },
      {
        description: '第一个参数为null时应该返回false',
        city1: null,
        city2: 'Toronto',
        expected: false
      },
      {
        description: '第二个参数为null时应该返回false',
        city1: 'Toronto',
        city2: null,
        expected: false
      },
      {
        description: '第一个参数为undefined时应该返回false',
        city1: undefined,
        city2: 'Toronto',
        expected: false
      },
      {
        description: '数字类型输入应该被正确处理',
        city1: 123,
        city2: '123',
        expected: true
      },
      {
        description: '相同数字类型输入应该返回true',
        city1: 123,
        city2: 123,
        expected: true
      },

      # 相似度测试
      {
        description: '轻微拼写错误(1个字符差异)应该返回true',
        city1: 'Toranto',
        city2: 'Toronto',
        expected: true
      },
      {
        description: '轻微拼写错误(字符位置错误)应该返回false',
        city1: 'Toronot',
        city2: 'Toronto',
        expected: false
      },
      {
        description: '较大拼写错误(2个字符差异，超过10%)应该返回false',
        city1: 'Toranot',
        city2: 'Toronto',
        expected: false
      },
      {
        description: 'Hamilton轻微拼写错误应该返回true',
        city1: 'Hamiltn',
        city2: 'Hamilton',
        expected: true
      },

      # 子城市关系测试
      {
        description: 'Scarborough应该被识别为Toronto的子城市',
        city1: 'Scarborough',
        city2: 'Toronto',
        expected: true
      },
      {
        description: 'Toronto应该被识别为Scarborough的主城市',
        city1: 'Toronto',
        city2: 'Scarborough',
        expected: true
      },
      {
        description: 'North York应该被识别为Toronto的子城市',
        city1: 'North York',
        city2: 'Toronto',
        expected: true
      },
      {
        description: 'Toronto应该被识别为North York的主城市',
        city1: 'Toronto',
        city2: 'North York',
        expected: true
      },
      {
        description: 'Etobicoke应该被识别为Toronto的子城市',
        city1: 'Etobicoke',
        city2: 'Toronto',
        expected: true
      },
      {
        description: 'Toronto应该被识别为Etobicoke的主城市',
        city1: 'Toronto',
        city2: 'Etobicoke',
        expected: true
      },
      {
        description: 'East York应该被识别为Toronto的子城市',
        city1: 'East York',
        city2: 'Toronto',
        expected: true
      },
      {
        description: 'Toronto应该被识别为East York的主城市',
        city1: 'Toronto',
        city2: 'East York',
        expected: true
      },
      {
        description: 'Ancaster应该被识别为Hamilton的子城市',
        city1: 'Ancaster',
        city2: 'Hamilton',
        expected: true
      },
      {
        description: 'Hamilton应该被识别为Dundas的主城市',
        city1: 'Hamilton',
        city2: 'Dundas',
        expected: true
      },
      {
        description: 'Stoney Creek应该被识别为Hamilton的子城市',
        city1: 'Stoney Creek',
        city2: 'Hamilton',
        expected: true
      },

      # 子城市拼写错误容错测试
      {
        description: '子城市与主城市拼写错误时应该返回true',
        city1: 'Scarborough',
        city2: 'Toranto',
        expected: true
      },
      {
        description: '主城市拼写错误与子城市时应该返回false',
        city1: 'Toronot',
        city2: 'North York',
        expected: false
      },
      {
        description: '子城市拼写错误与主城市时应该返回true',
        city1: 'Scarborogh',
        city2: 'Toronto',
        expected: true
      },
      {
        description: '主城市与子城市拼写错误时应该返回true',
        city1: 'Toronto',
        city2: 'Noth York',
        expected: true
      },
      {
        description: 'Hamilton子城市拼写错误时应该返回true',
        city1: 'Ancastr',
        city2: 'Hamilton',
        expected: true
      },

      # 不相关城市测试
      {
        description: '不相关的城市应该返回false - Scarborough vs Vancouver',
        city1: 'Scarborough',
        city2: 'Vancouver',
        expected: false
      },
      {
        description: '不相关的城市应该返回false - Hamilton vs Calgary',
        city1: 'Hamilton',
        city2: 'Calgary',
        expected: false
      },
      {
        description: '不相关的城市应该返回false - Toronto vs Montreal',
        city1: 'Toronto',
        city2: 'Montreal',
        expected: false
      },
      {
        description: '子城市与不相关城市应该返回false',
        city1: 'North York',
        city2: 'Vancouver',
        expected: false
      }
    ]

    tests.forEach (test) ->
      it "#{test.description} - #{test.city1} vs #{test.city2}", (done) ->
        result = cityHelper.isSameCity(test.city1, test.city2)
        result.should.equal(test.expected)
        done()

  describe 'Custom similarity threshold test', ->
    # 自定义相似度阈值测试
    customThresholdTests = [
      {
        description: '使用严格阈值(5%)时轻微拼写错误应该返回true',
        city1: 'Toranto',
        city2: 'Toronto',
        threshold: 0.05,
        expected: true
      },
      {
        description: '使用宽松阈值(20%)时较大拼写错误应该返回false',
        city1: 'Toranot',
        city2: 'Toronto',
        threshold: 0.2,
        expected: false
      },
      {
        description: '使用严格阈值(5%)时Hamilton拼写错误应该返回true',
        city1: 'Hamiltn',
        city2: 'Hamilton',
        threshold: 0.05,
        expected: true
      },
      {
        description: '使用适中阈值(15%)时Hamilton拼写错误应该返回true',
        city1: 'Hamiltn',
        city2: 'Hamilton',
        threshold: 0.15,
        expected: true
      }
    ]

    customThresholdTests.forEach (test) ->
      testName = "#{test.description} - #{test.city1} vs #{test.city2}"
      it "#{testName} (threshold: #{test.threshold})", (done) ->
        result = cityHelper.isSameCity(test.city1, test.city2, test.threshold)
        result.should.equal(test.expected)
        done()
