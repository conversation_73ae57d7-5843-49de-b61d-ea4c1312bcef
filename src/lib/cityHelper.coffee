#NOTE: used in libapp/proerties, lib dont have MODEL

helpers_string = require './helpers_string'
{levenshteinDistance} = helpers_string
countryCodes = require './countrycodes'
countryFull2Abbr = countryCodes.countryFull2Abbr
countryAbbrToFull = countryCodes.countryAbbrToFull
debugHelper = require './debug'
staticCityFields = require './staticCityFields'
debug = debugHelper.getDebugger()
abbr2Full =
  'CA':
    'ON':	'ONTARIO'
    'BC':	'BRITISH COLUMBIA'
    'AB':	'ALBERTA'
    'MB':	'MANITOBA'
    'NB':	'NEW BRUNSWICK'
    'NF':	'NEWFOUNDLAND AND LABRADOR' # found in DDF office
    'NL':	'NEWFOUNDLAND AND LABRADOR' # for reverse searching
    # 'NL': 'Newfoundland & Labrador'
    'NT':	'NORTHWEST TERRITORIES'
    'NS':	'NOVA SCOTIA'
    'NU':	'NUNAVUT'
    'PE':	'PRINCE EDWARD ISLAND'
    'QC':	'QUEBEC'
    # 'QC': 'Québec'
    'SK':	'SASKATCHEWAN'
    'YT':	'YUKON'


full2Abbr = {'CA':{}}
for cnty,provs of abbr2Full
  for abbr, full of provs
    full2Abbr[cnty][full] = abbr

#full->abbr mapping需要设置
full2Abbr['CA']['NEWFOUNDLAND AND LABRADOR'] = 'NL'
full2Abbr['CA']['NEWFOUNDLAND & LABRADOR'] = 'NL'
full2Abbr['CA']['NEWFOUNDLAND LABRADOR'] = 'NL'
full2Abbr['CA']['NEWFOUNDLAND'] = 'NL'
full2Abbr['CA']['QUÉBEC'] = 'QC'

module.exports.abbr2Full = abbr2Full

#remove NF in prov list
module.exports.provAbbrArray=(cnty)->
  provs = Object.keys(abbr2Full[cnty or 'CA'])
  provs = provs.filter (p)->
    return p isnt 'NF'
  return provs

# 20210915,@Julie,not used for now, may support world country later.
# module.exports.loadAllProv = loadAllProv = (boundaryWorld, cb)->
#   boundaryWorld.findToArray {},{fields:{cnty:1,prov:1,name:1}}, (err, ret)->
#     return cb(err) if err
#     for obj in ret
#       full2Abbr[obj.cnty]?={}
#       full2Abbr[obj.cnty][obj.name] = obj.prov
#     # debug.debug 'full2Abbr',full2Abbr
#     return cb()


module.exports.provMap = abbr2Full


module.exports.preload = preload = (cb)->
  Coll.findToArray {},->

module.exports.getProvFullName = (prov='',cnty='CA')->
  # NOTE: null is object
  if 'object' is typeof prov
    cnty = prov?.cnty or 'CA'
    prov = prov?.prov
  return null unless prov
  cnty = cnty.trim().toUpperCase()
  prov = prov.trim().toUpperCase()
  return helpers_string.strCapitalize(abbr2Full[cnty]?[prov] or prov, true)

module.exports.getProvAbbrName = getProvAbbrName = (prov='',cnty='CA')->
  # NOTE: null is object
  # BUG: in filterBanners -> provAndCity.coffee.getProvAbbrName
  if 'object' is typeof prov
    cnty = prov?.cnty or 'CA'
    prov = prov?.prov
  return null unless prov
  cnty = cnty.trim().toUpperCase()
  prov = prov.trim().toUpperCase()
  return full2Abbr[cnty]?[prov] or prov


module.exports.getCntyAbbrName = (cnty)->
  if 'object' is typeof cnty
    {cnty} = cnty
  return null unless cnty
  cnty = cnty.replace(/\./g,'')
  cnty = cnty.trim().toUpperCase()
  return countryFull2Abbr[cnty] or cnty.toUpperCase()

module.exports.getCntyFullName = (cnty)->
  if 'object' is typeof cnty
    {cnty} = cnty
  return null unless cnty
  cnty = cnty.replace(/\./g,'')
  cnty = cnty.trim().toUpperCase()
  return helpers_string.strCapitalize(countryAbbrToFull[cnty] or cnty, true)

###
# replace correct symbol in city
# @param {String} city
###
module.exports.replaceSymbols = (city)->
  # Whitchurch–Stouffville -> Whitchurch-Stouffville
  symbolMapping = {
    '–': '-',#'en dash'->'hyphen'
  }
  for k,v of symbolMapping
    city = city.replace(k,v)
  return city
#TODO: confirm with fred and sales. remove undealed city
module.exports.GTA_CITIES = {
  'CA:ON:AJAX':1
  'CA:ON:BRAMPTON':1
  'CA:ON:CALEDON':1
  'CA:ON:PICKERING':1
  'CA:ON:PELHAM':1
  'CA:ON:INNISFIL':1
  'CA:ON:MILTON':1
  'CA:ON:HAMILTON':1
  'CA:ON:COLLINGWOOD':1
  'CA:ON:CLARINGTON':1
  'CA:ON:WELLAND':1
  'CA:ON:AURORA':1
  'CA:ON:ADJALA-TOSORONTIO':1
  'CA:ON:NEW TECUMSETH':1
  'CA:ON:UXBRIDGE':1
  'CA:ON:CHIPPEWAS OF GEORGINA ISLAND FIRST NATION':1
  'CA:ON:MISSISSAUGAS OF SCUGOG ISLAND':1
  'CA:ON:MISSISSAUGA':1
  'CA:ON:WEST LINCOLN':1
  'CA:ON:WAINFLEET':1
  'CA:ON:SEVERN':1
  'CA:ON:RAMARA':1
  'CA:ON:WHITBY':1
  'CA:ON:HALTON HILLS':1
  'CA:ON:ESSA':1
  'CA:ON:BRADFORD WEST GWILLIMBURY':1
  'CA:ON:SPRINGWATER':1
  'CA:ON:NIAGARA FALLS':1
  'CA:ON:BURLINGTON':1
  'CA:ON:ORO-MEDONTE':1
  'CA:ON:EAST GWILLIMBURY':1
  'CA:ON:OAKVILLE':1
  'CA:ON:BROCK':1
  'CA:ON:THOROLD':1
  'CA:ON:OSHAWA':1
  'CA:ON:FORT ERIE':1
  'CA:ON:MARKHAM':1
  'CA:ON:TORONTO':1
  'CA:ON:VAUGHAN':1
  'CA:ON:ST. CATHARINES':1
  'CA:ON:WHITCHURCH-STOUFFVILLE':1
  'CA:ON:LINCOLN':1
  'CA:ON:CLEARVIEW':1
  'CA:ON:KING':1
  'CA:ON:GEORGINA':1
  'CA:ON:NEWMARKET':1
  'CA:ON:PORT COLBORNE':1
  'CA:ON:NIAGARA-ON-THE-LAKE':1
  'CA:ON:GRIMSBY':1
  'CA:ON:RICHMOND HILL':1
  'CA:ON:PENETANGUISHENE':1
  'CA:ON:ORILLIA':1
  'CA:ON:CHRISTIAN ISLAND 30':1
  'CA:ON:CHRISTIAN ISLAND 30A':1
  'CA:ON:TINY':1
  'CA:ON:CHIPPEWAS OF RAMA FIRST NATION':1
  'CA:ON:TAY':1
  'CA:ON:MIDLAND':1
  'CA:ON:WASAGA BEACH':1
  'CA:ON:BARRIE':1
  'CA:ON:SCUGOG':1
}

#TODO:rollback commented cities after Jnue 1st.
module.exports.RM_CONTROL_CITIES = {
  # 'CA:ON:AJAX':1
  # 'CA:ON:BRAMPTON':1
  'CA:ON:CALEDON':1
  # 'CA:ON:PICKERING':1
  'CA:ON:INNISFIL':1
  # 'CA:ON:MILTON':1
  # 'CA:ON:HAMILTON':1
  'CA:ON:CLARINGTON':1
  # 'CA:ON:AURORA':1
  'CA:ON:NEW TECUMSETH':1
  'CA:ON:UXBRIDGE':1
  # 'CA:ON:MISSISSAUGA':1
  # 'CA:ON:WHITBY':1
  # 'CA:ON:HALTON HILLS':1
  'CA:ON:BRADFORD WEST GWILLIMBURY':1
  'CA:ON:BURLINGTON':1
  # 'CA:ON:EAST GWILLIMBURY':1
  # 'CA:ON:OAKVILLE':1
  # 'CA:ON:OSHAWA':1
  # 'CA:ON:MARKHAM':1
  # 'CA:ON:TORONTO':1
  # 'CA:ON:VAUGHAN':1
  # 'CA:ON:WHITCHURCH-STOUFFVILLE':1
  # 'CA:ON:KING':1
  # 'CA:ON:GEORGINA':1
  # 'CA:ON:NEWMARKET':1
  # 'CA:ON:RICHMOND HILL':1
  # 'CA:ON:BARRIE':1
}

###
current city which have top agents.
[
	"Ajax",
	"Ancaster",
	"Aurora",
	"Barrie",
	"Brampton",
	"Burlington",
	"Burnaby",
	"Calgary",
	"Coquitlam",
	"East Gwillimbury",
	"Fort Erie",
	"Georgina",
	"Grimsby",
	"Guelph",
	"Haldimand",
	"Halifax",
	"Hamilton",
	"King",
	"Langley",
	"Lincoln",
	"London",
	"Markham",
	"Milton",
	"Mississauga",
	"Newmarket",
	"Niagara Falls",
	"Niagara-On-The-Lake",
	"North Vancouver",
	"Oakville",
	"Oshawa",
	"Ottawa",
	"Pickering",
	"Port Colborne",
	"Richmond",
	"Richmond Hill",
	"St. Catharines",
	"Surrey",
	"Thorold",
	"Toronto",
	"Vancouver",
	"Vaughan",
	"Waterloo",
	"Welland",
	"West Vancouver",
	"Whitby",
	"Whitchurch-Stouffville",
	"Windsor"
]

###

# 判断是不是subcity，如果是返回对应的主city
# @param {string} 要判断的city
# return {result:false}/{result:true,city:xxxx}
module.exports.isSubCityAndGetParentCity = isSubCityAndGetParentCity = (cityOrigin='') ->
  for prov,value of staticCityFields.subCityNameList
    for k,v of value
      if cityOrigin.trim() in v
        return {result:true,city:k}
  return {result:false}

###
# 判断两个城市是否为同一城市
# @param {string} city1 - 第一个城市名称
# @param {string} city2 - 第二个城市名称
# @param {number} allowDiffRatio - 允许的差异比例，默认0.1 (10%)
# @return {boolean} - 如果是同一城市返回true，否则返回false
###
module.exports.isSameCity = isSameCity = (city1='', city2='', allowDiffRatio = 0.1) ->
  # 参数验证
  if (not city1) or (not city2)
    return false

  # 强制转换为字符串并去除两边空白字符
  city1 = (city1 + '').trim()
  city2 = (city2 + '').trim()

  # 1. 城市名称完全一致
  if city1.toUpperCase() is city2.toUpperCase()
    return true

  # 2. 城市相似度检查，参考 isSimilarAddr 的逻辑
  if isCitySimilar(city1, city2, allowDiffRatio)
    return true

  # 3. 检查子城市关系
  # 检查 city1 是否为 city2 的子城市
  subCityResult1 = isSubCityAndGetParentCity(city1)
  if subCityResult1.result and (subCityResult1.city.toUpperCase() is city2.toUpperCase())
    return true

  # 检查 city2 是否为 city1 的子城市
  subCityResult2 = isSubCityAndGetParentCity(city2)
  if subCityResult2.result and (subCityResult2.city.toUpperCase() is city1.toUpperCase())
    return true

  # 4. 子城市判断需要考虑拼写错误问题
  # 如果其中一个是子城市，检查另一个是否与主城市相似
  if subCityResult1.result
    parentCity = subCityResult1.city
    if isCitySimilar(parentCity, city2, allowDiffRatio)
      return true

  if subCityResult2.result
    parentCity = subCityResult2.city
    if isCitySimilar(parentCity, city1, allowDiffRatio)
      return true

  # 5. 检查是否有子城市名称拼写错误的情况
  # 遍历所有子城市，检查是否与输入的城市相似
  for prov, cities of staticCityFields.subCityNameList
    for mainCity, subCities of cities
      # 检查 city1 是否与某个子城市相似，city2 是否与对应主城市相似
      for subCity in subCities
        # city1 是子城市，city2 是主城市
        city1IsSubCity = isCitySimilar(city1, subCity, allowDiffRatio)
        city2IsMainCity = isCitySimilar(city2, mainCity, allowDiffRatio)
        if city1IsSubCity and city2IsMainCity
          return true
        # city2 是子城市，city1 是主城市
        city2IsSubCity = isCitySimilar(city2, subCity, allowDiffRatio)
        city1IsMainCity = isCitySimilar(city1, mainCity, allowDiffRatio)
        if city2IsSubCity and city1IsMainCity
          return true

  return false

###
# 判断两个城市名称是否相似，参考 isSimilarAddr 的逻辑
# @param {string} city1 - 第一个城市名称
# @param {string} city2 - 第二个城市名称
# @param {number} allowDiffRatio - 允许的差异比例
# @return {boolean} - 如果相似返回true，否则返回false
###
isCitySimilar = (city1='', city2='', allowDiffRatio = 0.1) ->
  if (not city1) or (not city2)
    return false

  # 强制转换格式并标准化
  city1 = (city1 + '').trim().replace(/\s+/g, ' ').toUpperCase()
  city2 = (city2 + '').trim().replace(/\s+/g, ' ').toUpperCase()

  # 完全一致
  if city1 is city2
    return true

  # 分词比较，参考 isSimilarAddr 的逻辑
  city1Arr = city1.split(' ')
  city2Arr = city2.split(' ')

  # 如果词数差异超过1，认为不相似
  if (Math.abs city1Arr.length - city2Arr.length) > 1
    return false

  # 找出不同的词
  diffArr = city1Arr
    .filter((x) -> not(city2Arr.includes(x)))
    .concat(city2Arr.filter((x) -> not(city1Arr.includes(x))))

  if diffArr.length > 2
    return false
  else if diffArr.length is 2
    # 如果有2个不同的词，检查它们的编辑距离
    diff = levenshteinDistance diffArr[0], diffArr[1]
    # 使用整个城市名称的长度来计算阈值，与 isSimilarAddr 的逻辑一致
    maxDiffCount = Math.ceil(city2.length * allowDiffRatio)
    if diff > maxDiffCount
      return false
  # diffArr.length is 1 或 0 时认为是相似的

  return true